"""
Loss functions for regression tasks.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class HuberLoss(nn.Module):
    """Huber loss for robust regression."""
    
    def __init__(self, delta: float = 1.0):
        super().__init__()
        self.delta = delta
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        residual = torch.abs(predictions - targets)
        condition = residual < self.delta
        squared_loss = 0.5 * residual ** 2
        linear_loss = self.delta * residual - 0.5 * self.delta ** 2
        return torch.where(condition, squared_loss, linear_loss).mean()


class SmoothL1Loss(nn.Module):
    """Smooth L1 loss (similar to Huber but different formulation)."""
    
    def __init__(self, beta: float = 1.0):
        super().__init__()
        self.beta = beta
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        return F.smooth_l1_loss(predictions, targets, beta=self.beta)


class MAPELoss(nn.Module):
    """Mean Absolute Percentage Error loss."""
    
    def __init__(self, epsilon: float = 1e-8):
        super().__init__()
        self.epsilon = epsilon
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        return torch.mean(torch.abs((targets - predictions) / (targets + self.epsilon))) * 100


class LogCoshLoss(nn.Module):
    """Log-cosh loss for regression."""
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        diff = predictions - targets
        return torch.mean(torch.log(torch.cosh(diff)))


class QuantileLoss(nn.Module):
    """Quantile loss for quantile regression."""
    
    def __init__(self, quantile: float = 0.5):
        super().__init__()
        self.quantile = quantile
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        errors = targets - predictions
        return torch.mean(torch.max(
            (self.quantile - 1) * errors,
            self.quantile * errors
        ))


class CombinedLoss(nn.Module):
    """Combination of multiple loss functions."""
    
    def __init__(self, losses: Dict[str, nn.Module], weights: Dict[str, float]):
        super().__init__()
        self.losses = nn.ModuleDict(losses)
        self.weights = weights
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        total_loss = 0
        for name, loss_fn in self.losses.items():
            weight = self.weights.get(name, 1.0)
            total_loss += weight * loss_fn(predictions, targets)
        return total_loss


def get_loss_function(config: Dict[str, Any]) -> nn.Module:
    """
    Get loss function based on configuration.
    
    Args:
        config: Loss configuration dictionary
        
    Returns:
        Loss function
    """
    loss_type = config.get("type", "mse").lower()
    
    if loss_type == "mse":
        return nn.MSELoss()
    
    elif loss_type == "mae" or loss_type == "l1":
        return nn.L1Loss()
    
    elif loss_type == "huber":
        delta = config.get("huber_delta", 1.0)
        return HuberLoss(delta=delta)
    
    elif loss_type == "smooth_l1":
        beta = config.get("smooth_l1_beta", 1.0)
        return SmoothL1Loss(beta=beta)
    
    elif loss_type == "mape":
        epsilon = config.get("mape_epsilon", 1e-8)
        return MAPELoss(epsilon=epsilon)
    
    elif loss_type == "log_cosh":
        return LogCoshLoss()
    
    elif loss_type == "quantile":
        quantile = config.get("quantile", 0.5)
        return QuantileLoss(quantile=quantile)
    
    elif loss_type == "combined":
        # Example: {"mse": 0.7, "mae": 0.3}
        loss_weights = config.get("weights", {"mse": 1.0})
        losses = {}
        
        for loss_name in loss_weights.keys():
            if loss_name == "mse":
                losses[loss_name] = nn.MSELoss()
            elif loss_name == "mae":
                losses[loss_name] = nn.L1Loss()
            elif loss_name == "huber":
                losses[loss_name] = HuberLoss(delta=config.get("huber_delta", 1.0))
            # Add more as needed
        
        return CombinedLoss(losses, loss_weights)
    
    else:
        logger.warning(f"Unknown loss type: {loss_type}. Using MSE loss.")
        return nn.MSELoss()


class FocalLoss(nn.Module):
    """
    Focal loss adapted for regression tasks.
    Focuses on hard examples by down-weighting easy ones.
    """
    
    def __init__(self, alpha: float = 1.0, gamma: float = 2.0, reduction: str = "mean"):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        # Compute base loss (MSE)
        mse_loss = F.mse_loss(predictions, targets, reduction="none")
        
        # Compute focal weight
        # Use normalized MSE as "probability" for focal weighting
        normalized_mse = mse_loss / (mse_loss.max() + 1e-8)
        focal_weight = self.alpha * (1 - torch.exp(-normalized_mse)) ** self.gamma
        
        # Apply focal weighting
        focal_loss = focal_weight * mse_loss
        
        if self.reduction == "mean":
            return focal_loss.mean()
        elif self.reduction == "sum":
            return focal_loss.sum()
        else:
            return focal_loss


class AdaptiveLoss(nn.Module):
    """
    Adaptive loss that automatically adjusts between different loss functions
    based on the magnitude of errors.
    """
    
    def __init__(self, threshold: float = 1.0):
        super().__init__()
        self.threshold = threshold
        self.mse_loss = nn.MSELoss(reduction="none")
        self.mae_loss = nn.L1Loss(reduction="none")
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        mse = self.mse_loss(predictions, targets)
        mae = self.mae_loss(predictions, targets)
        
        # Use MSE for small errors, MAE for large errors
        errors = torch.abs(predictions - targets)
        use_mse = errors < self.threshold
        
        adaptive_loss = torch.where(use_mse, mse, mae)
        return adaptive_loss.mean()
